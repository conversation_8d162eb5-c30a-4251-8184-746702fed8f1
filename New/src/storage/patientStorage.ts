/**
 * Patient storage utilities for DermaCare bi-directional sync service
 *
 * This module provides advanced patient storage operations that replicate the
 * functionality of v3Integration's Contact.searchCreateOrUpdate() method.
 * It implements smart patient search and upsert logic with fallback matching
 * to ensure complete feature parity with the legacy system.
 *
 * **Key Features:**
 * - Smart patient search with priority-based matching (apId → ccId → email → phone)
 * - Upsert operations that create or update existing records
 * - Maintains data integrity across both CC and AP platforms
 * - Optimized database queries with proper indexing
 * - Comprehensive error handling and logging
 *
 * **Search Priority Logic:**
 * 1. Search by AP ID (highest priority)
 * 2. Search by CC ID
 * 3. Search by email address
 * 4. Search by phone number (lowest priority)
 *
 * **Data Integrity:**
 * - Preserves existing platform-specific data when updating
 * - <PERSON>les null/undefined values gracefully
 * - Maintains proper timestamps for sync coordination
 * - Validates required fields before operations
 *
 * @example
 * ```typescript
 * // Search and update patient with CC data
 * const patient = await searchCreateOrUpdatePatient({
 *   ccId: 12345,
 *   ccData: ccPatientData,
 *   email: "<EMAIL>",
 *   phone: "+**********"
 * });
 *
 * // Search and update patient with AP data
 * const patient = await searchCreateOrUpdatePatient({
 *   apId: "ap_contact_123",
 *   apData: apContactData,
 *   email: "<EMAIL>"
 * });
 * ```
 *
 * @since 1.0.0
 * @version 1.0.0
 */

import { getDb } from "@database";
import { patient } from "@database/schema";
import type {
	GetAPContactType,
	GetCCPatientType,
} from "@type";
import { logError } from "@utils/errorLogger";
import { safeTimestampToDate } from "@utils/validation";
import { eq, or } from "drizzle-orm";

/**
 * Cleans email and phone values like v3Integration Contact model
 * Converts empty strings to null, matching the v3Integration behavior
 *
 * @param value - Email or phone value to clean
 * @returns Cleaned value (null if empty/whitespace)
 */
function cleanContactValue(value: string | null | undefined): string | null {
	if (!value || value.trim() === '') {
		return null;
	}
	return value.trim();
}

/**
 * Patient search and upsert payload interface
 *
 * Defines the structure for patient search and upsert operations.
 * Supports data from both CC and AP platforms with flexible matching criteria.
 */
export interface PatientUpsertPayload {
	/** AutoPatient contact ID for direct AP matching */
	apId?: string;
	/** CliniCore patient ID for direct CC matching */
	ccId?: number;
	/** Email address for fallback matching */
	email?: string;
	/** Phone number for fallback matching */
	phone?: string;
	/** Complete CC patient data for storage */
	ccData?: GetCCPatientType;
	/** Complete AP contact data for storage */
	apData?: GetAPContactType;
	/** Source platform identifier */
	source?: "ap" | "cc";
}

/**
 * Searches for existing patient or creates new one with smart matching logic
 *
 * This function replicates the core functionality of v3Integration's
 * Contact.searchCreateOrUpdate() method. It implements a priority-based
 * search strategy to find existing patients and either updates them with
 * new data or creates a new patient record if none is found.
 *
 * **Search Strategy:**
 * 1. **Direct ID Match**: First attempts to find patient by platform-specific ID
 *    - If apId provided: searches by patient.apId
 *    - If ccId provided: searches by patient.ccId
 * 2. **Fallback Matching**: If no direct ID match, searches by contact information
 *    - Searches by email address
 *    - Searches by phone number
 *    - Uses OR logic to find any matching contact info
 *
 * **Update Logic:**
 * - Preserves existing data while updating with new information
 * - Only updates fields that are provided in the payload
 * - Maintains proper timestamps for sync coordination
 * - Updates both platform-specific data and common fields
 *
 * **Create Logic:**
 * - Creates new patient record if no existing patient found
 * - Populates all provided fields
 * - Sets appropriate timestamps
 * - Generates UUID for internal patient ID
 *
 * @param payload - Patient data and search criteria
 * @param payload.apId - AP contact ID for direct matching
 * @param payload.ccId - CC patient ID for direct matching
 * @param payload.email - Email for fallback matching
 * @param payload.phone - Phone for fallback matching
 * @param payload.ccData - Complete CC patient data
 * @param payload.apData - Complete AP contact data
 * @param payload.source - Source platform ("ap" or "cc")
 *
 * @returns Promise resolving to patient record (existing or newly created)
 *
 * @throws {Error} When database operations fail
 * @throws {Error} When no search criteria provided
 *
 * @example
 * ```typescript
 * // Search by CC ID and update with new data
 * const patient = await searchCreateOrUpdatePatient({
 *   ccId: 12345,
 *   ccData: updatedCCData,
 *   email: "<EMAIL>",
 *   source: "cc"
 * });
 *
 * // Search by email and create if not found
 * const patient = await searchCreateOrUpdatePatient({
 *   email: "<EMAIL>",
 *   phone: "+**********",
 *   apData: apContactData,
 *   source: "ap"
 * });
 *
 * // Search with fallback logic
 * const patient = await searchCreateOrUpdatePatient({
 *   apId: "ap_123", // Try AP ID first
 *   email: "<EMAIL>", // Fallback to email
 *   ccData: ccPatientData
 * });
 * ```
 */
export async function searchCreateOrUpdatePatient(
	payload: PatientUpsertPayload,
): Promise<typeof patient.$inferSelect> {
	try {
		const db = getDb();
		let existingPatient: typeof patient.$inferSelect | undefined;

		// Priority 1: Search by platform-specific IDs
		if (payload.apId) {
			console.log(`Searching patient by AP ID: ${payload.apId}`);
			const results = await db
				.select()
				.from(patient)
				.where(eq(patient.apId, payload.apId))
				.limit(1);
			existingPatient = results[0];
		} else if (payload.ccId) {
			console.log(`Searching patient by CC ID: ${payload.ccId}`);
			const results = await db
				.select()
				.from(patient)
				.where(eq(patient.ccId, payload.ccId))
				.limit(1);
			existingPatient = results[0];
		}

		// CRITICAL FIX: v3Integration search logic (lines 76-83 from Contact.searchCreateOrUpdate)
		// Search by email first, then by phone (sequential, not OR logic)
		if (!existingPatient && payload.email) {
			console.log(`Searching patient by email: ${payload.email}`);
			const results = await db
				.select()
				.from(patient)
				.where(eq(patient.email, payload.email))
				.limit(1);
			existingPatient = results[0];
		}

		if (!existingPatient && payload.phone) {
			console.log(`Searching patient by phone: ${payload.phone}`);
			const results = await db
				.select()
				.from(patient)
				.where(eq(patient.phone, payload.phone))
				.limit(1);
			existingPatient = results[0];
		}

		// Update existing patient or create new one
		if (existingPatient) {
			console.log(`Updating existing patient: ${existingPatient.id}`);
			
			// Prepare update data - only update fields that are provided
			const updateData: Partial<typeof patient.$inferInsert> = {
				updatedAt: new Date(),
			};

			// Update platform-specific IDs
			if (payload.apId) updateData.apId = payload.apId;
			if (payload.ccId) updateData.ccId = payload.ccId;

			// Update contact information with v3Integration-style cleaning
			if (payload.email !== undefined) updateData.email = cleanContactValue(payload.email);
			if (payload.phone !== undefined) updateData.phone = cleanContactValue(payload.phone);

			// Update platform-specific data and timestamps
			if (payload.ccData) {
				updateData.ccData = payload.ccData;
				updateData.ccUpdatedAt = safeTimestampToDate(
					payload.ccData.updatedAt,
					"updatedAt",
					"Patient",
					payload.ccId || "unknown"
				);
			}
			if (payload.apData) {
				updateData.apData = payload.apData;
				updateData.apUpdatedAt = safeTimestampToDate(
					payload.apData.dateUpdated || payload.apData.dateAdded,
					"dateUpdated/dateAdded",
					"Patient",
					payload.apId || "unknown"
				);
			}

			const [updatedPatient] = await db
				.update(patient)
				.set(updateData)
				.where(eq(patient.id, existingPatient.id))
				.returning();

			console.log(`Patient updated successfully: ${updatedPatient.id}`);
			return updatedPatient;
		} else {
			console.log(`Creating new patient`);

			// Validate that we have enough data to create a patient (using cleaned values)
			const cleanedEmail = cleanContactValue(payload.email);
			const cleanedPhone = cleanContactValue(payload.phone);
			if (!cleanedEmail && !cleanedPhone) {
				throw new Error("Cannot create patient: email or phone is required");
			}

			// AGGRESSIVE FIX: Use a completely bulletproof upsert approach
			// First, try to find any existing patient that might conflict
			let existingConflict = null;

			if (payload.ccId) {
				const ccResults = await db
					.select()
					.from(patient)
					.where(eq(patient.ccId, payload.ccId))
					.limit(1);
				existingConflict = ccResults[0];
			}

			if (!existingConflict && payload.apId) {
				const apResults = await db
					.select()
					.from(patient)
					.where(eq(patient.apId, payload.apId))
					.limit(1);
				existingConflict = apResults[0];
			}

			// NUCLEAR FIX: Prepare insert/update data with unique constraint workaround
			const patientData = {
				email: cleanContactValue(payload.email),
				phone: cleanContactValue(payload.phone),
				ccId: payload.ccId || null,
				// CRITICAL: Never set apId to null to avoid unique constraint issues
				// Use a unique placeholder that won't conflict
				apId: payload.apId || `temp_${Date.now()}_${Math.random().toString(36).substring(2, 11)}_${payload.ccId || 'unknown'}`,
				ccData: payload.ccData || null,
				apData: payload.apData || null,
				ccUpdatedAt: payload.ccData ? safeTimestampToDate(
					payload.ccData.updatedAt,
					"updatedAt",
					"Patient",
					payload.ccId || "unknown"
				) : null,
				apUpdatedAt: payload.apData ? safeTimestampToDate(
					payload.apData.dateUpdated || payload.apData.dateAdded,
					"dateUpdated/dateAdded",
					"Patient",
					payload.apId || "unknown"
				) : null,
				updatedAt: new Date(),
			};

			if (existingConflict) {
				// Update existing patient
				console.log(`Updating existing conflicting patient: ${existingConflict.id}`);
				const [updatedPatient] = await db
					.update(patient)
					.set(patientData)
					.where(eq(patient.id, existingConflict.id))
					.returning();
				return updatedPatient;
			} else {
				// Create new patient with multiple fallback attempts
				for (let attempt = 1; attempt <= 3; attempt++) {
					try {
						const [newPatient] = await db
							.insert(patient)
							.values(patientData)
							.returning();

						console.log(`New patient created successfully on attempt ${attempt}: ${newPatient.id}`);
						return newPatient;
					} catch (insertError) {
						console.log(`Insert attempt ${attempt} failed:`, insertError);

						if (attempt === 3) {
							// Final attempt failed, try one more search and update
							if (payload.ccId) {
								const finalResults = await db
									.select()
									.from(patient)
									.where(eq(patient.ccId, payload.ccId))
									.limit(1);
								if (finalResults[0]) {
									console.log(`Final fallback: found existing patient by ccId: ${finalResults[0].id}`);
									const [updatedPatient] = await db
										.update(patient)
										.set(patientData)
										.where(eq(patient.id, finalResults[0].id))
										.returning();
									return updatedPatient;
								}
							}
							throw insertError;
						}

						// Wait a bit before retry to handle race conditions
						await new Promise(resolve => setTimeout(resolve, 100 * attempt));
					}
				}
			}
		}

		// This should never be reached, but TypeScript requires it
		throw new Error("Failed to create or find patient after all attempts");
	} catch (error) {
		await logError(
			"PATIENT_SEARCH_CREATE_UPDATE_ERROR",
			error,
			{
				payload: {
					apId: payload.apId,
					ccId: payload.ccId,
					email: payload.email,
					phone: payload.phone,
					source: payload.source,
				},
			},
			"PatientStorage",
		);
		throw error;
	}
}

/**
 * Finds patient by multiple criteria with priority-based search
 *
 * This is a read-only version of searchCreateOrUpdatePatient that only
 * searches for existing patients without creating or updating records.
 * Useful for lookup operations where you don't want to modify data.
 *
 * @param criteria - Search criteria with same priority logic
 * @returns Promise resolving to patient record or null if not found
 */
export async function findPatientByMultipleCriteria(
	criteria: Pick<PatientUpsertPayload, "apId" | "ccId" | "email" | "phone">,
): Promise<typeof patient.$inferSelect | null> {
	try {
		const db = getDb();

		// Priority 1: Search by platform-specific IDs
		if (criteria.apId) {
			const results = await db
				.select()
				.from(patient)
				.where(eq(patient.apId, criteria.apId))
				.limit(1);
			return results[0] || null;
		}

		if (criteria.ccId) {
			const results = await db
				.select()
				.from(patient)
				.where(eq(patient.ccId, criteria.ccId))
				.limit(1);
			return results[0] || null;
		}

		// Priority 2: Fallback search by email and phone
		if (criteria.email || criteria.phone) {
			const conditions = [];
			if (criteria.email) {
				conditions.push(eq(patient.email, criteria.email));
			}
			if (criteria.phone) {
				conditions.push(eq(patient.phone, criteria.phone));
			}

			if (conditions.length > 0) {
				const results = await db
					.select()
					.from(patient)
					.where(or(...conditions))
					.limit(1);
				return results[0] || null;
			}
		}

		return null;
	} catch (error) {
		await logError(
			"PATIENT_FIND_ERROR",
			error,
			{ criteria },
			"PatientStorage",
		);
		throw error;
	}
}
