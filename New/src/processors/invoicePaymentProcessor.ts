import { getDb } from "@database";
import { patient } from "@database/schema";
import type { GetCCPatientType, GetPaymentType, WebhookContext } from "@type";
import {
	checkAndAddToBuffer,
	generateInvoicePaymentBufferKey,
} from "@utils/bufferManager";
import { logSyncError } from "@utils/errorLogger";
import { validateAndFormatTimestamp } from "@utils/validation";
import { eq } from "drizzle-orm";
import { apClient } from "../api/apClient";
import { ccClient } from "../api/ccClient";
import { processPatientCreate } from "./patientProcessor";

/**
 * Custom field names for AP invoice data
 * Based on v3Integration/utils/apCustomfields.ts
 */
const AP_INVOICE_CUSTOM_FIELDS = {
	LatestInvoicePDFURL: "Latest Invoice PDF URL",
	LastInvoiceGrossAmount: "Latest Gross Amount",
	LastInvoiceDiscount: "Latest Discount",
	LastInvoiceTotalAmount: "Latest Total Amount",
	LatestPaymentStatus: "Latest Payment Status",
	LastInvoiceProducts: "Latest Products",
	LastInvoiceDiagnosis: "Latest Diagnosis",
	LastInvoiceTreatedBy: "Latest Treated By",
};

/**
 * Custom field names for AP payment data
 */
const AP_PAYMENT_CUSTOM_FIELDS = {
	LatestPaymentStatus: "Latest Payment Status",
	LatestAmountPaid: "Latest Amount Paid",
	LatestPaymentDate: "Latest Payment Date",
	LatestPaymentPDFURL: "Latest Payment PDF URL",
};

/**
 * Custom field names for AP LTV (Lifetime Value) data
 */
const AP_LTV_CUSTOM_FIELDS = {
	LTV: "LTV",
	TotalInvoiceAmount: "Total Invoice Amount",
	TotalPaidAmount: "Total Paid Amount",
	TotalDueAmount: "Total Due Amount",
};

/**
 * Processes invoice and payment events from CC with automatic patient dependency management
 * Equivalent to ProcessInvoicePayment job from v3Integration
 *
 * **Patient Dependency Management:**
 * - Automatically fetches and syncs patients from CC if they don't exist in local database
 * - Ensures patient exists and is synced to AP before processing invoice and payment data
 * - Uses processPatientCreate to handle patient sync with full error handling
 *
 * @param payload - Invoice or payment data from CC webhook
 * @param context - Webhook processing context
 * @returns Processing result
 */
export async function processInvoicePayment(
	payload: GetPaymentType,
	context: WebhookContext,
): Promise<{ success: boolean; message: string }> {
	try {
		console.log(`Processing invoice/payment for patient: ${payload.patient}`);

		// Validate payment timestamp fields early to catch issues before database operations
		const timestampValidations = [
			validateAndFormatTimestamp(payload.createdAt, "createdAt", "Payment", payload.id),
			validateAndFormatTimestamp(payload.date, "date", "Payment", payload.id),
			validateAndFormatTimestamp(payload.updatedAt, "updatedAt", "Payment", payload.id),
		];

		const invalidTimestamps = timestampValidations.filter(v => !v.isValid);
		if (invalidTimestamps.length > 0) {
			const errorDetails = invalidTimestamps.map(v => v.error).join("; ");
			console.warn(`Payment ${payload.id} has invalid timestamp fields: ${errorDetails}`);

			// Log the validation issues but continue processing with fallback handling
			console.log(`Continuing payment processing with timestamp validation warnings for Payment ${payload.id}`);
		}

		// Check buffer to prevent duplicate processing
		const bufferKey = generateInvoicePaymentBufferKey(
			"ProcessInvoicePayment",
			payload.id || payload.patient,
		);
		if (checkAndAddToBuffer(bufferKey)) {
			return {
				success: true,
				message: `Invoice/payment processing recently completed, skipping. Patient ID: ${payload.patient}`,
			};
		}

		const db = getDb();

		// Find patient in database
		let dbPatient = await db
			.select()
			.from(patient)
			.where(eq(patient.ccId, payload.patient))
			.limit(1)
			.then((results) => results[0]);

		// Patient Dependency Management: If patient doesn't exist, fetch and sync from CC first
		if (!dbPatient) {
			console.log(
				`Patient not found for CC Patient ID: ${payload.patient}. Fetching and syncing patient first...`,
			);

			try {
				// Fetch patient data from CC API
				const ccPatient = await ccClient.patient.get(payload.patient);
				console.log(
					`✅ Fetched CC patient: ${ccPatient.firstName} ${ccPatient.lastName} (${ccPatient.email})`,
				);

				// Create webhook context for patient sync
				const patientSyncContext: WebhookContext = {
					event: {
						event: "EntityWasCreated",
						model: "Patient",
						id: ccPatient.id,
						payload: ccPatient,
					},
					processedAt: new Date(),
					requestId: context.requestId,
				};

				// Trigger patient sync to AP using processPatientCreate
				const patientSyncResult = await processPatientCreate(
					ccPatient,
					patientSyncContext,
				);

				if (!patientSyncResult.success) {
					const message = `Failed to sync patient before invoice/payment processing. CC Patient ID: ${payload.patient}, Error: ${patientSyncResult.message}`;
					console.error(message);
					return {
						success: false,
						message,
					};
				}

				console.log(
					`✅ Patient synced successfully: ${patientSyncResult.message}`,
				);

				// Re-fetch the patient from database after sync
				dbPatient = await db
					.select()
					.from(patient)
					.where(eq(patient.ccId, payload.patient))
					.limit(1)
					.then((results) => results[0]);

				if (!dbPatient) {
					const message = `Patient still not found after sync. CC Patient ID: ${payload.patient}`;
					console.error(message);
					return {
						success: false,
						message,
					};
				}

				console.log(
					`✅ Patient found after sync: ${dbPatient.id} (AP ID: ${dbPatient.apId}, CC ID: ${dbPatient.ccId})`,
				);
			} catch (error) {
				const message = `Failed to fetch or sync patient from CC. Patient ID: ${payload.patient}, Error: ${error instanceof Error ? error.message : String(error)}`;
				console.error(message);
				await logSyncError(
					"INVOICE_PAYMENT_PATIENT_DEPENDENCY_FAILED",
					error,
					payload.patient,
					"CC",
					"InvoicePaymentProcessor",
				);
				return {
					success: false,
					message,
				};
			}
		}

		// Additional check: Ensure patient is synced to AP
		if (!dbPatient.apId) {
			const message = `Patient exists but not synced to AP. CC Patient ID: ${payload.patient}`;
			console.log(message);
			return {
				success: false,
				message,
			};
		}

		// Sync invoice and payment data to AP custom fields
		const syncResult = await syncInvoicePaymentData(dbPatient);

		if (syncResult.success) {
			console.log(
				`Invoice/payment data synced successfully for patient: ${payload.patient}`,
			);
			return {
				success: true,
				message: `Invoice/payment data synced successfully for patient: ${payload.patient}`,
			};
		} else {
			await logSyncError(
				"INVOICE_PAYMENT_SYNC_FAILED",
				new Error(syncResult.message),
				payload.patient,
				"CC",
				"InvoicePaymentProcessor",
			);

			return {
				success: false,
				message: `Failed to sync invoice/payment data: ${syncResult.message}`,
			};
		}
	} catch (error) {
		// Enhanced error handling for timestamp validation issues
		const errorMessage = error instanceof Error ? error.message : String(error);

		// Check if this is a timestamp validation error
		if (errorMessage.includes("Invalid time value") ||
			errorMessage.includes("RangeError") ||
			errorMessage.includes("PgTimestamp.mapToDriverValue")) {

			console.error(`Timestamp validation error in invoice/payment processing:`, {
				paymentId: payload.id,
				patientId: payload.patient,
				error: errorMessage,
				paymentData: {
					createdAt: payload.createdAt,
					date: payload.date,
					updatedAt: payload.updatedAt,
				}
			});

			await logSyncError(
				"INVOICE_PAYMENT_TIMESTAMP_ERROR",
				new Error(`Timestamp validation failed for Payment ${payload.id}: ${errorMessage}`),
				payload.patient || payload.id,
				"CC",
				"InvoicePaymentProcessor",
			);

			return {
				success: false,
				message: `Invoice/payment processing failed due to invalid timestamp data. Payment ID: ${payload.id}, Patient ID: ${payload.patient}. Please check the date fields in ChiroTouch for this payment.`,
			};
		}

		// Handle other errors normally
		await logSyncError(
			"INVOICE_PAYMENT_ERROR",
			error,
			payload.patient || payload.id,
			"CC",
			"InvoicePaymentProcessor",
		);

		throw error;
	}
}

/**
 * Syncs invoice and payment data from CC to AP custom fields
 *
 * @param dbPatient - Patient record from database
 * @returns Sync result
 */
async function syncInvoicePaymentData(
	dbPatient: typeof patient.$inferSelect,
): Promise<{ success: boolean; message: string }> {
	try {
		if (!dbPatient.ccId || !dbPatient.apId) {
			return {
				success: false,
				message: "Patient missing CC ID or AP ID",
			};
		}

		// Get fresh patient data from CC
		const ccPatient = await ccClient.patient.get(dbPatient.ccId);

		// Validate CC patient timestamp before database operation
		const ccUpdatedAtValidation = validateAndFormatTimestamp(
			ccPatient.updatedAt,
			"updatedAt",
			"Patient",
			ccPatient.id
		);

		if (!ccUpdatedAtValidation.isValid) {
			console.warn(`CC Patient timestamp validation failed: ${ccUpdatedAtValidation.error}`);
		}

		// Update patient record with fresh CC data
		const db = getDb();
		await db
			.update(patient)
			.set({
				ccData: ccPatient,
				ccUpdatedAt: ccUpdatedAtValidation.date, // Use validated date or null
				updatedAt: new Date(),
			})
			.where(eq(patient.id, dbPatient.id));

		// Sync invoice data
		const invoiceResult = await syncInvoiceData(dbPatient.apId, ccPatient);
		if (!invoiceResult.success) {
			return invoiceResult;
		}

		// Sync payment data
		const paymentResult = await syncPaymentData(dbPatient.apId, ccPatient);
		if (!paymentResult.success) {
			return paymentResult;
		}

		return {
			success: true,
			message: "Invoice and payment data synced successfully",
		};
	} catch (error) {
		const errorMessage = error instanceof Error ? error.message : String(error);
		console.error(`Failed to sync invoice/payment data:`, error);

		return {
			success: false,
			message: `Failed to sync invoice/payment data: ${errorMessage}`,
		};
	}
}

/**
 * Syncs invoice data to AP custom fields
 *
 * @param apContactId - AP contact ID
 * @param ccPatient - CC patient data
 * @returns Sync result
 */
async function syncInvoiceData(
	apContactId: string,
	ccPatient: GetCCPatientType,
): Promise<{ success: boolean; message: string }> {
	try {
		if (!ccPatient.invoices || ccPatient.invoices.length === 0) {
			console.log(`No invoices to sync for patient: ${ccPatient.id}`);
			return {
				success: true,
				message: "No invoices to sync",
			};
		}

		// Get the latest invoice
		const latestInvoiceId = ccPatient.invoices[ccPatient.invoices.length - 1];
		const latestInvoice = await ccClient.invoice.get(latestInvoiceId);

		// Calculate invoice totals
		const grossAmount =
			latestInvoice.positions?.reduce(
				(sum, pos) => sum + (pos.gross || 0),
				0,
			) || 0;
		const discount = latestInvoice.discount || 0;
		const totalAmount = grossAmount - discount;

		// Prepare custom field updates
		const customFields = [
			{
				name: AP_INVOICE_CUSTOM_FIELDS.LatestInvoicePDFURL,
				value: latestInvoice.pdfUrl || "",
			},
			{
				name: AP_INVOICE_CUSTOM_FIELDS.LastInvoiceGrossAmount,
				value: grossAmount.toString(),
			},
			{
				name: AP_INVOICE_CUSTOM_FIELDS.LastInvoiceDiscount,
				value: discount.toString(),
			},
			{
				name: AP_INVOICE_CUSTOM_FIELDS.LastInvoiceTotalAmount,
				value: totalAmount.toString(),
			},
			{
				name: AP_INVOICE_CUSTOM_FIELDS.LastInvoiceProducts,
				value: latestInvoice.positions?.map((pos) => pos.name).join(", ") || "",
			},
			{
				name: AP_INVOICE_CUSTOM_FIELDS.LastInvoiceDiagnosis,
				value:
					latestInvoice.diagnoses?.map((diag) => diag.text).join(", ") || "",
			},
		];

		// Update AP contact with custom fields
		await updateAPCustomFields(apContactId, customFields);

		return {
			success: true,
			message: "Invoice data synced successfully",
		};
	} catch (error) {
		const errorMessage = error instanceof Error ? error.message : String(error);
		console.error(`Failed to sync invoice data:`, error);

		return {
			success: false,
			message: `Failed to sync invoice data: ${errorMessage}`,
		};
	}
}

/**
 * Syncs payment data to AP custom fields
 *
 * @param apContactId - AP contact ID
 * @param ccPatient - CC patient data
 * @returns Sync result
 */
async function syncPaymentData(
	apContactId: string,
	ccPatient: GetCCPatientType,
): Promise<{ success: boolean; message: string }> {
	try {
		if (!ccPatient.payments || ccPatient.payments.length === 0) {
			console.log(`No payments to sync for patient: ${ccPatient.id}`);
			return {
				success: true,
				message: "No payments to sync",
			};
		}

		// Get all payments
		const payments = await ccClient.payment.getMultiple(ccPatient.payments);

		if (payments.length === 0) {
			return {
				success: true,
				message: "No payments found",
			};
		}

		// Calculate LTV (Lifetime Value)
		const totalPaid = payments.reduce(
			(sum, payment) => sum + (payment.gross || 0),
			0,
		);

		// Get latest payment
		const latestPayment = payments[payments.length - 1];
		const latestAmount = latestPayment.invoicePayments?.length
			? latestPayment.invoicePayments[latestPayment.invoicePayments.length - 1]
					.gross
			: latestPayment.gross;

		// Validate payment timestamp for AP custom field
		const paymentDateValidation = validateAndFormatTimestamp(
			latestPayment.createdAt,
			"createdAt",
			"Payment",
			latestPayment.id
		);

		// Use validated ISO string or fallback to current date for AP custom field
		const paymentDateValue = paymentDateValidation.isValid
			? paymentDateValidation.isoString!
			: new Date().toISOString();

		if (!paymentDateValidation.isValid) {
			console.warn(`Payment date validation failed, using current date as fallback: ${paymentDateValidation.error}`);
		}

		// Prepare custom field updates
		const customFields = [
			{
				name: AP_PAYMENT_CUSTOM_FIELDS.LatestPaymentStatus,
				value: "Success",
			},
			{
				name: AP_PAYMENT_CUSTOM_FIELDS.LatestAmountPaid,
				value: Math.abs(latestAmount || 0).toString(),
			},
			{
				name: AP_PAYMENT_CUSTOM_FIELDS.LatestPaymentDate,
				value: paymentDateValue, // Use validated timestamp
			},
			{
				name: AP_PAYMENT_CUSTOM_FIELDS.LatestPaymentPDFURL,
				value: latestPayment.pdfUrl || "",
			},
			{
				name: AP_LTV_CUSTOM_FIELDS.LTV,
				value: Math.abs(totalPaid).toString(),
			},
			{
				name: AP_LTV_CUSTOM_FIELDS.TotalPaidAmount,
				value: Math.abs(totalPaid).toString(),
			},
		];

		// Update AP contact with custom fields
		await updateAPCustomFields(apContactId, customFields);

		return {
			success: true,
			message: "Payment data synced successfully",
		};
	} catch (error) {
		const errorMessage = error instanceof Error ? error.message : String(error);
		console.error(`Failed to sync payment data:`, error);

		return {
			success: false,
			message: `Failed to sync payment data: ${errorMessage}`,
		};
	}
}

/**
 * Updates AP contact custom fields
 *
 * @param apContactId - AP contact ID
 * @param customFields - Array of custom field updates
 */
async function updateAPCustomFields(
	apContactId: string,
	customFields: Array<{ name: string; value: string }>,
): Promise<void> {
	try {
		// Get all AP custom fields to map names to IDs
		const apCustomFields = await apClient.customField.all();
		const fieldNameToId = new Map(
			apCustomFields.map((field) => [field.name, field.id]),
		);

		// Prepare custom field updates with IDs
		const customFieldUpdates = [];

		for (const field of customFields) {
			let fieldId = fieldNameToId.get(field.name);

			// Create custom field if it doesn't exist
			if (!fieldId) {
				console.log(`Creating custom field: ${field.name}`);
				const newField = await apClient.customField.create({
					name: field.name,
					dataType: "TEXT",
					model: "contact",
				});
				fieldId = newField.id;
				fieldNameToId.set(field.name, fieldId);
			}

			customFieldUpdates.push({
				id: fieldId,
				value: field.value,
			});
		}

		// Update contact with custom fields
		if (customFieldUpdates.length > 0) {
			await apClient.contact.update(apContactId, {
				customFields: customFieldUpdates,
			});
			console.log(
				`Updated ${customFieldUpdates.length} custom fields for contact: ${apContactId}`,
			);
		}
	} catch (error) {
		console.error(`Failed to update AP custom fields:`, error);
		throw error;
	}
}
